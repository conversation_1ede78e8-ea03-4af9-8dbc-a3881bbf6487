<?php

namespace Tests\Feature\Dashboard;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\Dashboard\Widgets\Navigation\OwnTopic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class OwnTopicToggleTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $user = User::factory()->create([
            'thema_speichern' => 'Test topic for frequency calculation'
        ]);
        
        Auth::login($user);
    }

    /** @test */
    public function it_can_toggle_frequency_generator_view()
    {
        $widget = [
            'id' => 'test-widget-1',
            'pool_type' => 1,
            'title' => 'Test Own Topic Widget'
        ];

        Livewire::test(OwnTopic::class, [
            'poolId' => 1,
            'widget' => $widget
        ])
        ->assertSet('showFrequencyGenerator', false)
        ->call('toggleFrequencyGenerator')
        ->assertSet('showFrequencyGenerator', true)
        ->call('toggleFrequencyGenerator')
        ->assertSet('showFrequencyGenerator', false);
    }

    /** @test */
    public function it_initializes_with_correct_default_values()
    {
        $widget = [
            'id' => 'test-widget-2',
            'pool_type' => 1,
            'title' => 'Test Own Topic Widget'
        ];

        Livewire::test(OwnTopic::class, [
            'poolId' => 1,
            'widget' => $widget
        ])
        ->assertSet('showFrequencyGenerator', false)
        ->assertSet('poolId', 1)
        ->assertSet('widget', $widget)
        ->assertSet('topicText', 'Test topic for frequency calculation');
    }

    /** @test */
    public function it_renders_both_views_correctly()
    {
        $widget = [
            'id' => 'test-widget-3',
            'pool_type' => 1,
            'title' => 'Test Own Topic Widget'
        ];

        // Test initial state (Own Topic view visible)
        $component = Livewire::test(OwnTopic::class, [
            'poolId' => 1,
            'widget' => $widget
        ]);

        $component->assertSee('nav-widget-topicTab-test-widget-3')
                  ->assertSee('nav-widget-frequencyGeneratorTab-test-widget-3');

        // Test toggled state (Frequency Generator view visible)
        $component->call('toggleFrequencyGenerator')
                  ->assertSet('showFrequencyGenerator', true);
    }

    /** @test */
    public function it_maintains_topic_text_when_toggling()
    {
        $widget = [
            'id' => 'test-widget-4',
            'pool_type' => 1,
            'title' => 'Test Own Topic Widget'
        ];

        $testTopic = 'My test topic for frequency';

        Livewire::test(OwnTopic::class, [
            'poolId' => 1,
            'widget' => $widget
        ])
        ->set('topicText', $testTopic)
        ->assertSet('topicText', $testTopic)
        ->call('toggleFrequencyGenerator')
        ->assertSet('showFrequencyGenerator', true)
        ->assertSet('topicText', $testTopic) // Topic text should be preserved
        ->call('toggleFrequencyGenerator')
        ->assertSet('showFrequencyGenerator', false)
        ->assertSet('topicText', $testTopic); // Topic text should still be preserved
    }
}
