<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Livewire\Dashboard\Widgets\Navigation\OwnTopic;

class OwnTopicToggleTest extends TestCase
{
    /** @test */
    public function it_has_show_frequency_generator_property()
    {
        $component = new OwnTopic();
        
        $this->assertObjectHasProperty('showFrequencyGenerator', $component);
        $this->assertFalse($component->showFrequencyGenerator);
    }

    /** @test */
    public function it_has_toggle_frequency_generator_method()
    {
        $component = new OwnTopic();
        
        $this->assertTrue(method_exists($component, 'toggleFrequencyGenerator'));
    }

    /** @test */
    public function it_can_toggle_frequency_generator_state()
    {
        $component = new OwnTopic();
        
        // Initial state should be false
        $this->assertFalse($component->showFrequencyGenerator);
        
        // Toggle to true
        $component->toggleFrequencyGenerator();
        $this->assertTrue($component->showFrequencyGenerator);
        
        // Toggle back to false
        $component->toggleFrequencyGenerator();
        $this->assertFalse($component->showFrequencyGenerator);
    }

    /** @test */
    public function it_has_required_properties_for_embedded_component()
    {
        $component = new OwnTopic();
        
        $this->assertObjectHasProperty('poolId', $component);
        $this->assertObjectHasProperty('widget', $component);
    }
}
